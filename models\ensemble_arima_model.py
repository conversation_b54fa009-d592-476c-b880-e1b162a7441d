#!/usr/bin/env python
"""
Ensemble ARIMA Model

This module implements an ensemble of ARIMA models for improved time series forecasting.
"""

import os
import numpy as np
import pandas as pd
from pathlib import Path
import pickle
import logging
import warnings
from typing import Dict, List, Optional, Tuple, Any, Union

# Suppress sklearn deprecation warnings
warnings.filterwarnings("ignore", message=".*force_all_finite.*", category=FutureWarning)
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from pmdarima import auto_arima
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import ElasticNet
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Configure logging
logger = logging.getLogger(__name__)

class EnsembleARIMAModel:
    """
    Ensemble ARIMA model that combines multiple ARIMA models with different configurations.
    """

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the ensemble ARIMA model.

        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.models = []
        self.meta_model = None
        self.meta_model_name = None
        self.model_weights = None
        self.scaler = StandardScaler()
        self.feature_scaler = StandardScaler()
        self.pca = None  # Store PCA transformer for consistent feature reduction
        self.expected_features = None  # Store expected feature count
        self.use_pca = False  # Flag to track if PCA is used

    def build(self):
        """
        Build the ensemble model.
        """
        logger.info("Building Ensemble ARIMA model")

        # The actual building happens during training
        # This method is included for API compatibility
        pass

    def train(self, train_data: np.ndarray, exog: Optional[np.ndarray] = None):
        """
        Train the ensemble model.

        Args:
            train_data: Training data
            exog: Exogenous variables (optional)
        """
        logger.info("Training Ensemble ARIMA model")

        # Get configuration parameters
        n_models = self.config.get('n_models', 5)
        d = self.config.get('d', 1)
        seasonal = self.config.get('seasonal', False)
        seasonal_m = self.config.get('seasonal_m', 12)

        # Ensure train_data is a 1D array
        if isinstance(train_data, pd.Series):
            train_data = train_data.values

        if len(train_data.shape) > 1 and train_data.shape[1] > 1:
            train_data = train_data.flatten()

        # Scale the training data
        train_data_scaled = self.scaler.fit_transform(train_data.reshape(-1, 1)).flatten()

        # Scale exogenous variables if provided
        exog_scaled = None
        if exog is not None:
            # Ensure exog has the same number of samples as train_data
            if len(exog) != len(train_data_scaled):
                logger.warning(f"Exog shape {exog.shape} doesn't match train_data shape {train_data_scaled.shape}")
                # Trim exog to match train_data length
                if len(exog) > len(train_data_scaled):
                    logger.info(f"Trimming exog from {len(exog)} to {len(train_data_scaled)} samples")
                    exog = exog[:len(train_data_scaled)]
                else:
                    logger.warning("Exog has fewer samples than train_data, cannot use exogenous variables")
                    exog = None

            if exog is not None:
                exog_scaled = self.feature_scaler.fit_transform(exog)
                logger.info(f"Using exogenous variables with shape {exog_scaled.shape}")

        # Train multiple ARIMA models with different configurations
        self.models = []

        # Model 1: Best-performing ARIMA(5,d,5) - based on previous results
        try:
            model1 = ARIMA(
                train_data_scaled,
                order=(5, d, 5),
                exog=exog_scaled
            ).fit()  # Remove method parameter
            self.models.append(('arima_5_d_5', model1))
            logger.info("Trained ARIMA(5,d,5) model - best performing configuration")
        except Exception as e:
            logger.warning(f"Failed to train ARIMA(5,d,5): {str(e)}")

        # Model 2: ARIMA(2,d,2) - balanced model
        try:
            model2 = ARIMA(
                train_data_scaled,
                order=(2, d, 2),
                exog=exog_scaled
            ).fit()
            self.models.append(('arima_2_d_2', model2))
            logger.info("Trained ARIMA(2,d,2) model")
        except Exception as e:
            logger.warning(f"Failed to train ARIMA(2,d,2): {str(e)}")

        # Model 3: ARIMA(5,d,0) - AR focused
        try:
            model3 = ARIMA(
                train_data_scaled,
                order=(5, d, 0),
                exog=exog_scaled
            ).fit()
            self.models.append(('arima_5_d_0', model3))
            logger.info("Trained ARIMA(5,d,0) model")
        except Exception as e:
            logger.warning(f"Failed to train ARIMA(5,d,0): {str(e)}")

        # Model 4: ARIMA(0,d,5) - MA focused
        try:
            model4 = ARIMA(
                train_data_scaled,
                order=(0, d, 5),
                exog=exog_scaled
            ).fit()
            self.models.append(('arima_0_d_5', model4))
            logger.info("Trained ARIMA(0,d,5) model")
        except Exception as e:
            logger.warning(f"Failed to train ARIMA(0,d,5): {str(e)}")

        # Model 5: Auto ARIMA with improved settings
        try:
            model5 = auto_arima(
                train_data_scaled,
                start_p=1,
                start_q=1,
                max_p=5,
                max_q=5,
                d=d,
                seasonal=seasonal,
                m=seasonal_m if seasonal else 1,
                exogenous=exog_scaled,
                stepwise=False,
                random=True,
                random_state=42,
                information_criterion='aic',  # Use AIC for model selection
                with_intercept=True,  # Include intercept term
                max_order=10  # Limit total order to prevent overfitting
                # Remove method, n_fits and maxiter parameters
            )
            self.models.append(('auto_arima', model5))
            logger.info(f"Trained Auto ARIMA model with order {model5.order}")
        except Exception as e:
            logger.warning(f"Failed to train Auto ARIMA: {str(e)}")

        # Model 6: ARIMA(4,d,2) - another good configuration
        try:
            model6 = ARIMA(
                train_data_scaled,
                order=(4, d, 2),
                exog=exog_scaled
            ).fit()
            self.models.append(('arima_4_d_2', model6))
            logger.info("Trained ARIMA(4,d,2) model")
        except Exception as e:
            logger.warning(f"Failed to train ARIMA(4,d,2): {str(e)}")

        # Model 7: ARIMA(3,d,3) - balanced model
        try:
            model7 = ARIMA(
                train_data_scaled,
                order=(3, d, 3),
                exog=exog_scaled
            ).fit()
            self.models.append(('arima_3_d_3', model7))
            logger.info("Trained ARIMA(3,d,3) model")
        except Exception as e:
            logger.warning(f"Failed to train ARIMA(3,d,3): {str(e)}")

        # Train meta-model if we have enough base models
        if len(self.models) >= 2:
            self._train_meta_model(train_data, exog)

        logger.info(f"Trained {len(self.models)} ARIMA models for ensemble")

    def _train_meta_model(self, train_data: np.ndarray, exog: Optional[np.ndarray] = None):
        """
        Train a meta-model to combine predictions from base models.

        Args:
            train_data: Training data
            exog: Exogenous variables (optional)
        """
        # Get predictions from all base models
        base_predictions = []
        model_names = []

        for name, model in self.models:
            try:
                if hasattr(model, 'predict_in_sample'):
                    pred = model.predict_in_sample()
                elif hasattr(model, 'fittedvalues'):
                    pred = model.fittedvalues
                else:
                    # For auto_arima
                    pred = model.predict_in_sample()

                # Inverse transform if needed
                if name != 'auto_arima':  # statsmodels ARIMA
                    pred = self.scaler.inverse_transform(pred.reshape(-1, 1)).flatten()

                base_predictions.append(pred)
                model_names.append(name)
                logger.info(f"Added {name} predictions to meta-model training data")
            except Exception as e:
                logger.warning(f"Error getting in-sample predictions for {name}: {str(e)}")

        if not base_predictions:
            logger.warning("No base predictions available for meta-model training")
            return

        # Prepare meta-model training data
        X_meta = np.column_stack(base_predictions)

        # Calculate model weights based on individual performance
        model_weights = []
        for i, pred in enumerate(base_predictions):
            # Calculate MSE for this model
            mse = np.mean((train_data - pred) ** 2)
            # Convert to weight (lower MSE = higher weight)
            weight = 1.0 / (mse + 1e-10)  # Add small epsilon to avoid division by zero
            model_weights.append(weight)
            logger.info(f"Model {model_names[i]} MSE: {mse:.2f}, Weight: {weight:.6f}")

        # Normalize weights to sum to 1
        total_weight = sum(model_weights)
        model_weights = [w / total_weight for w in model_weights]
        logger.info(f"Normalized model weights: {[f'{w:.4f}' for w in model_weights]}")

        # Store model weights for weighted averaging fallback
        self.model_weights = model_weights

        # Add exogenous variables if available
        if exog is not None:
            # Process exogenous variables consistently
            exog_processed = self._process_exogenous_variables(exog, fit_transform=True)
            if exog_processed is not None:
                X_meta = np.column_stack([X_meta, exog_processed])

        # Try different meta-models and select the best one
        from sklearn.model_selection import TimeSeriesSplit
        from sklearn.metrics import mean_squared_error

        # Define candidate meta-models
        from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
        from sklearn.linear_model import ElasticNet, Ridge

        meta_models = [
            ('gradient_boosting', GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, max_depth=3, random_state=42)),
            ('random_forest', RandomForestRegressor(n_estimators=100, max_depth=5, random_state=42)),
            ('extra_trees', ExtraTreesRegressor(n_estimators=100, max_depth=5, random_state=42)),
            ('elastic_net', ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42)),
            ('ridge', Ridge(alpha=1.0, random_state=42))
        ]

        # Use time series cross-validation to evaluate meta-models
        tscv = TimeSeriesSplit(n_splits=5)
        best_score = float('inf')
        best_model = None
        best_model_name = None

        for name, model in meta_models:
            try:
                scores = []
                for train_idx, test_idx in tscv.split(X_meta):
                    X_train, X_test = X_meta[train_idx], X_meta[test_idx]
                    y_train, y_test = train_data[train_idx], train_data[test_idx]

                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_test)
                    score = mean_squared_error(y_test, y_pred)
                    scores.append(score)

                avg_score = np.mean(scores)
                logger.info(f"Meta-model {name} average MSE: {avg_score:.2f}")

                if avg_score < best_score:
                    best_score = avg_score
                    best_model = model
                    best_model_name = name
            except Exception as e:
                logger.warning(f"Error evaluating meta-model {name}: {str(e)}")

        if best_model is not None:
            logger.info(f"Selected {best_model_name} as the best meta-model with MSE: {best_score:.2f}")
            # Train the best model on all data
            best_model.fit(X_meta, train_data)
            self.meta_model = best_model
            self.meta_model_name = best_model_name
        else:
            # Fallback to gradient boosting if no model was selected
            logger.warning("No meta-model was selected, falling back to gradient boosting")
            self.meta_model = GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, max_depth=3, random_state=42)
            self.meta_model.fit(X_meta, train_data)
            self.meta_model_name = "gradient_boosting"

        logger.info(f"Trained {self.meta_model_name} meta-model for ensemble combination")

        # Store the expected number of features for consistency
        self.expected_features = X_meta.shape[1]
        logger.info(f"Meta-model trained with {self.expected_features} features")

        # Store feature processing configuration for prediction consistency
        if hasattr(self, 'use_pca') and self.use_pca:
            logger.info(f"PCA will be used for prediction with {self.pca.n_components_} components")

    def _process_exogenous_variables(self, exog: np.ndarray, fit_transform: bool = False) -> Optional[np.ndarray]:
        """
        Process exogenous variables consistently for training and prediction.

        Args:
            exog: Exogenous variables array
            fit_transform: Whether to fit the transformers (True for training, False for prediction)

        Returns:
            Processed exogenous variables or None if processing fails
        """
        if exog is None:
            return None

        try:
            # Check if we have enough data for meaningful processing
            if exog.shape[0] < 2:
                logger.warning(f"Insufficient data for exogenous processing (shape: {exog.shape}). Skipping exogenous variables.")
                return None

            # Determine if we need PCA based on feature count
            max_features = 10  # Maximum features to use without PCA

            if exog.shape[1] > max_features:
                # Use PCA to reduce dimensionality
                from sklearn.decomposition import PCA

                # Calculate appropriate number of components
                n_components = min(max_features, min(exog.shape[0] - 1, exog.shape[1]))

                if n_components > 0 and exog.shape[0] > n_components:
                    if fit_transform:
                        # Fit and transform during training
                        self.pca = PCA(n_components=n_components)
                        exog_reduced = self.pca.fit_transform(exog)
                        self.use_pca = True
                        logger.info(f"Fitted PCA: reduced from {exog.shape[1]} to {n_components} dimensions")
                    else:
                        # Transform during prediction
                        if self.use_pca and self.pca is not None:
                            exog_reduced = self.pca.transform(exog)
                            logger.info(f"Applied PCA: reduced from {exog.shape[1]} to {exog_reduced.shape[1]} dimensions")
                        else:
                            # Fallback: truncate to max_features
                            exog_reduced = exog[:, :max_features]
                            logger.warning(f"PCA not available, truncating to {max_features} features")

                    return exog_reduced
                else:
                    logger.warning(f"Insufficient data for PCA (shape: {exog.shape}). Using original features.")
                    # Truncate to max_features if too many
                    return exog[:, :max_features] if exog.shape[1] > max_features else exog
            else:
                # Use original features if count is reasonable
                return exog

        except Exception as e:
            logger.warning(f"Error processing exogenous variables: {str(e)}. Skipping exogenous variables.")
            return None

    def _fallback_prediction(self, base_predictions: list) -> np.ndarray:
        """
        Generate fallback predictions using weighted or simple average.

        Args:
            base_predictions: List of base model predictions

        Returns:
            Fallback predictions
        """
        if not base_predictions:
            logger.warning("No base predictions available for fallback")
            return None

        # Ensure all predictions have the same length
        pred_lengths = [len(pred) for pred in base_predictions]
        if len(set(pred_lengths)) > 1:
            logger.warning(f"Base predictions have different lengths: {pred_lengths}")
            # Use the minimum length
            min_length = min(pred_lengths)
            base_predictions = [pred[:min_length] for pred in base_predictions]
            logger.info(f"Truncated all predictions to length {min_length}")

        try:
            if hasattr(self, 'model_weights') and self.model_weights and len(self.model_weights) == len(base_predictions):
                # Use weighted average
                weighted_predictions = np.zeros_like(base_predictions[0])
                for i, pred in enumerate(base_predictions):
                    weighted_predictions += self.model_weights[i] * pred
                logger.info("Using weighted average for fallback predictions")
                return weighted_predictions
            else:
                # Use simple average
                simple_average = np.mean(base_predictions, axis=0)
                logger.info("Using simple average for fallback predictions")
                return simple_average
        except Exception as e:
            logger.warning(f"Error in fallback prediction: {str(e)}")
            # Return the first prediction as last resort
            if base_predictions:
                logger.info("Using first prediction as emergency fallback")
                return base_predictions[0]
            else:
                return None

    def _validate_and_reshape_exog(self, exog: np.ndarray, n_periods: int) -> Optional[np.ndarray]:
        """
        Validate and reshape exogenous variables for prediction.

        Args:
            exog: Raw exogenous variables
            n_periods: Expected number of periods

        Returns:
            Processed exogenous variables or None if validation fails
        """
        try:
            # Check if exog has the right shape
            if len(exog) != n_periods:
                logger.warning(f"Exog shape {exog.shape} doesn't match n_periods {n_periods}")
                # Try to adjust exog to match n_periods
                if len(exog) > n_periods:
                    logger.info(f"Trimming exog from {len(exog)} to {n_periods} samples")
                    exog = exog[:n_periods]
                else:
                    logger.warning("Exog has fewer samples than n_periods, cannot use exogenous variables")
                    return None

            # Handle different exog shapes
            if len(exog.shape) == 3:
                # Reshape from (batch, seq, features) to (batch, features)
                exog = exog.reshape(exog.shape[0], -1)
                logger.info(f"Reshaped exog from 3D to 2D: {exog.shape}")
            elif len(exog.shape) == 1:
                # Single sample, reshape to (1, features)
                exog = exog.reshape(1, -1)

            return exog

        except Exception as e:
            logger.warning(f"Error validating exog shape: {str(e)}")
            return None

    def _scale_exog_for_prediction(self, exog: np.ndarray) -> Optional[np.ndarray]:
        """
        Scale exogenous variables for prediction, handling dimension mismatches.

        Args:
            exog: Validated exogenous variables

        Returns:
            Scaled exogenous variables or None if scaling fails
        """
        try:
            # Check if feature scaler has been fitted
            if not hasattr(self.feature_scaler, 'n_features_in_'):
                logger.debug("Feature scaler has not been fitted, returning unscaled data")
                return exog  # Return unscaled data instead of None

            expected_features = self.feature_scaler.n_features_in_
            actual_features = exog.shape[1] if len(exog.shape) > 1 else exog.shape[0]

            logger.debug(f"Scaling exog: expected {expected_features} features, got {actual_features}")

            if actual_features != expected_features:
                # Try to adjust features to match expected dimensions
                if actual_features > expected_features:
                    # Truncate excess features
                    exog = exog[:, :expected_features]
                    logger.info(f"Truncated exog features from {actual_features} to {expected_features}")
                elif actual_features < expected_features:
                    # Pad with zeros for missing features
                    padding = np.zeros((exog.shape[0], expected_features - actual_features))
                    exog = np.column_stack([exog, padding])
                    logger.info(f"Padded exog features from {actual_features} to {expected_features}")

            # Validate the final shape before scaling
            if exog.shape[1] != expected_features:
                logger.warning(f"Final exog shape {exog.shape} doesn't match expected features {expected_features}")
                return None

            # Scale the adjusted exogenous variables
            try:
                # Check if the feature scaler expects a different number of features
                if hasattr(self.feature_scaler, 'n_features_in_'):
                    expected_scaler_features = self.feature_scaler.n_features_in_
                    if exog.shape[1] != expected_scaler_features:
                        logger.warning(f"Feature dimension mismatch for scaler: got {exog.shape[1]}, expected {expected_scaler_features}")
                        # Adjust the features to match what the scaler expects
                        if exog.shape[1] > expected_scaler_features:
                            # Truncate to match scaler expectations
                            exog = exog[:, :expected_scaler_features]
                            logger.info(f"Truncated exog features from {exog.shape[1]} to {expected_scaler_features} for scaler")
                        else:
                            # Pad with zeros to match scaler expectations
                            padding = np.zeros((exog.shape[0], expected_scaler_features - exog.shape[1]))
                            exog = np.column_stack([exog, padding])
                            logger.info(f"Padded exog features from {exog.shape[1]} to {expected_scaler_features} for scaler")

                exog_scaled = self.feature_scaler.transform(exog)
                return exog_scaled
            except Exception as transform_error:
                logger.warning(f"Feature scaler transform failed: {str(transform_error)}")
                # Try to understand what went wrong
                logger.debug(f"Exog shape before transform: {exog.shape}")
                logger.debug(f"Expected features: {expected_features}")

                # If transform fails, return the unscaled data for fallback use
                logger.warning("Feature scaler transform failed, returning unscaled data for fallback")
                return exog

        except Exception as e:
            logger.warning(f"Error scaling exogenous variables: {str(e)}")
            # Log more details about the error
            logger.debug(f"Exog shape: {exog.shape if exog is not None else 'None'}")
            logger.debug(f"Feature scaler fitted: {hasattr(self.feature_scaler, 'n_features_in_')}")
            if hasattr(self.feature_scaler, 'n_features_in_'):
                logger.debug(f"Expected features: {self.feature_scaler.n_features_in_}")
            return None

    def predict(self, n_periods: int, exog: Optional[np.ndarray] = None, X: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Make predictions with the ensemble model.

        Args:
            n_periods: Number of periods to predict
            exog: Exogenous variables (optional)
            X: Alternative name for exogenous variables (for compatibility with auto_arima)

        Returns:
            Predictions
        """
        # Handle X parameter for compatibility with auto_arima
        if exog is None and X is not None:
            exog = X

        if not self.models:
            logger.warning("No models in ensemble, returning zeros")
            return np.zeros(n_periods)

        # Process and scale exogenous variables if provided
        exog_scaled = None
        exog_processed = None
        if exog is not None:
            try:
                # Validate and reshape exog
                exog_processed = self._validate_and_reshape_exog(exog, n_periods)

                if exog_processed is not None:
                    # Scale the processed exogenous variables
                    exog_scaled = self._scale_exog_for_prediction(exog_processed)
                    if exog_scaled is not None:
                        logger.info(f"Using exogenous variables with shape {exog_scaled.shape} for prediction")
                    else:
                        logger.debug("Failed to scale exogenous variables, will use unscaled processed data")
                        # Use the processed but unscaled exog as fallback, but ensure it has the right number of features
                        exog_scaled = exog_processed

                        # Check if we need to adjust the number of features to match what models expect
                        if hasattr(self, 'feature_scaler') and hasattr(self.feature_scaler, 'n_features_in_'):
                            expected_scaler_features = self.feature_scaler.n_features_in_
                            if exog_scaled.shape[1] != expected_scaler_features:
                                logger.warning(f"Adjusting unscaled exog features from {exog_scaled.shape[1]} to {expected_scaler_features}")
                                if exog_scaled.shape[1] > expected_scaler_features:
                                    # Truncate to match scaler expectations
                                    exog_scaled = exog_scaled[:, :expected_scaler_features]
                                    logger.info(f"Truncated unscaled exog to {exog_scaled.shape}")
                                else:
                                    # Pad with zeros to match scaler expectations
                                    padding = np.zeros((exog_scaled.shape[0], expected_scaler_features - exog_scaled.shape[1]))
                                    exog_scaled = np.column_stack([exog_scaled, padding])
                                    logger.info(f"Padded unscaled exog to {exog_scaled.shape}")

                        logger.info(f"Using unscaled exogenous variables with shape {exog_scaled.shape} for prediction")
                else:
                    logger.warning("Failed to process exogenous variables, will use fallback for models that need exog")

            except Exception as e:
                logger.warning(f"Error processing exogenous variables: {str(e)}")
                exog_scaled = None
                exog_processed = None

        # Get predictions from all base models
        base_predictions = []
        for name, model in self.models:
            try:
                # Try with exogenous variables first, then fallback to without
                pred = None

                if name == 'auto_arima':
                    # For auto_arima
                    if exog_scaled is not None:
                        try:
                            pred = model.predict(n_periods=n_periods, X=exog_scaled)
                            logger.info(f"Made predictions with {name} model using exogenous variables")
                        except Exception as exog_error:
                            logger.warning(f"Error making predictions with {name} using exogenous variables: {str(exog_error)}")
                            pred = model.predict(n_periods=n_periods)
                            logger.info(f"Made predictions with {name} model without exogenous variables")
                    else:
                        pred = model.predict(n_periods=n_periods)
                        logger.info(f"Made predictions with {name} model")
                else:
                    # For statsmodels ARIMA/SARIMAX
                    # Check if model has exogenous variables (regression component)
                    # Multiple ways to detect if model was trained with exogenous variables
                    has_exog = False

                    # Method 1: Check if model has exog attribute
                    if hasattr(model, 'exog') and model.exog is not None:
                        has_exog = True
                        logger.debug(f"Model {name} has exog via exog attribute")

                    # Method 2: Check if model has k_exog attribute (number of exogenous variables)
                    elif hasattr(model, 'k_exog') and model.k_exog > 0:
                        has_exog = True
                        logger.debug(f"Model {name} has exog via k_exog: {model.k_exog}")

                    # Method 3: Check model specification
                    elif hasattr(model, 'specification') and hasattr(model.specification, 'k_exog') and model.specification.k_exog > 0:
                        has_exog = True
                        logger.debug(f"Model {name} has exog via specification.k_exog: {model.specification.k_exog}")

                    # Method 4: Try to make a prediction without exog and see if it fails
                    else:
                        try:
                            # Try a test prediction without exog
                            test_pred = model.forecast(steps=1)
                            has_exog = False
                            logger.debug(f"Model {name} does not require exog (test prediction succeeded)")
                        except Exception as test_error:
                            if "exog" in str(test_error).lower() or "regression component" in str(test_error).lower():
                                has_exog = True
                                logger.debug(f"Model {name} requires exog (test prediction failed with exog error)")
                            else:
                                has_exog = False
                                logger.debug(f"Model {name} test prediction failed for other reason: {str(test_error)}")

                    logger.debug(f"Model {name} has_exog determination: {has_exog}")

                    if has_exog:
                        # Model was trained with exogenous variables
                        if exog_scaled is not None:
                            try:
                                pred = model.forecast(steps=n_periods, exog=exog_scaled)
                                logger.info(f"Made predictions with {name} model using exogenous variables")
                            except Exception as exog_error:
                                logger.warning(f"Error making predictions with {name}: {str(exog_error)}")
                                # Try with fallback exogenous variables (zeros)
                                try:
                                    fallback_exog = np.zeros((n_periods, exog_scaled.shape[1] if exog_scaled is not None else 1))
                                    pred = model.forecast(steps=n_periods, exog=fallback_exog)
                                    logger.info(f"Made predictions with {name} model using fallback exogenous variables")
                                except Exception as fallback_error:
                                    logger.warning(f"Fallback prediction failed for {name}: {str(fallback_error)}")
                                    continue
                        else:
                            # Model requires exogenous variables but we don't have scaled ones - try fallback
                            logger.debug(f"Trying fallback for {name} - exog_processed: {exog_processed is not None}")

                            # Get the expected number of exogenous features from the model
                            try:
                                n_exog_features = None

                                # Try multiple methods to get the number of exogenous features
                                if hasattr(model, 'exog') and model.exog is not None:
                                    n_exog_features = model.exog.shape[1]
                                    logger.debug(f"Model {name} expects {n_exog_features} exogenous features (from exog)")
                                elif hasattr(model, 'k_exog') and model.k_exog > 0:
                                    n_exog_features = model.k_exog
                                    logger.debug(f"Model {name} expects {n_exog_features} exogenous features (from k_exog)")
                                elif hasattr(model, 'specification') and hasattr(model.specification, 'k_exog'):
                                    n_exog_features = model.specification.k_exog
                                    logger.debug(f"Model {name} expects {n_exog_features} exogenous features (from specification)")
                                else:
                                    # Try to infer from the original training data if available
                                    if hasattr(self, 'feature_scaler') and hasattr(self.feature_scaler, 'n_features_in_'):
                                        n_exog_features = self.feature_scaler.n_features_in_
                                        logger.debug(f"Model {name} using {n_exog_features} features from scaler")
                                    else:
                                        n_exog_features = 1  # Last resort fallback
                                        logger.debug(f"Model {name} exog info not available, using 1 feature")

                                # First try with processed but unscaled exog if available
                                if exog_processed is not None:
                                    logger.debug(f"Using processed exog with shape {exog_processed.shape}")
                                    # Adjust processed exog to match expected features
                                    if exog_processed.shape[1] > n_exog_features:
                                        fallback_exog = exog_processed[:, :n_exog_features]
                                        logger.debug(f"Truncated processed exog to {fallback_exog.shape}")
                                    elif exog_processed.shape[1] < n_exog_features:
                                        padding = np.zeros((exog_processed.shape[0], n_exog_features - exog_processed.shape[1]))
                                        fallback_exog = np.column_stack([exog_processed, padding])
                                        logger.debug(f"Padded processed exog to {fallback_exog.shape}")
                                    else:
                                        fallback_exog = exog_processed
                                        logger.debug(f"Using processed exog as-is: {fallback_exog.shape}")
                                else:
                                    # Last resort: use zero-filled exogenous variables
                                    fallback_exog = np.zeros((n_periods, n_exog_features))
                                    logger.debug(f"Using zero-filled exog: {fallback_exog.shape}")

                                # Make the prediction with fallback exog
                                try:
                                    pred = model.forecast(steps=n_periods, exog=fallback_exog)
                                    if exog_processed is not None:
                                        logger.info(f"Made predictions with {name} model using unscaled exogenous variables")
                                    else:
                                        logger.info(f"Made predictions with {name} model using zero-filled exogenous variables")
                                except Exception as forecast_error:
                                    # If forecast with exog fails, try without exog as last resort
                                    logger.warning(f"Forecast with fallback exog failed for {name}: {str(forecast_error)}")
                                    try:
                                        pred = model.forecast(steps=n_periods)
                                        logger.info(f"Made predictions with {name} model without exogenous variables (last resort)")
                                    except Exception as final_error:
                                        logger.warning(f"Final fallback prediction failed for {name}: {str(final_error)}")
                                        continue

                            except Exception as fallback_error:
                                logger.warning(f"Fallback prediction failed for {name}: {str(fallback_error)}")
                                logger.debug(f"Fallback error details: {type(fallback_error).__name__}: {str(fallback_error)}")
                                # Try one more time without exog
                                try:
                                    pred = model.forecast(steps=n_periods)
                                    logger.info(f"Made predictions with {name} model without exogenous variables (emergency fallback)")
                                except Exception as emergency_error:
                                    logger.warning(f"Emergency fallback failed for {name}: {str(emergency_error)}")
                                    continue
                    else:
                        # Model doesn't require exogenous variables
                        try:
                            pred = model.forecast(steps=n_periods)
                            logger.info(f"Made predictions with {name} model")
                        except Exception as e:
                            logger.warning(f"Error making predictions with {name}: {str(e)}")
                            continue

                # Inverse transform predictions
                if name != 'auto_arima' and pred is not None:  # statsmodels ARIMA
                    try:
                        if hasattr(self, 'scaler') and self.scaler is not None:
                            pred = self.scaler.inverse_transform(pred.reshape(-1, 1)).flatten()
                        else:
                            logger.info(f"No scaler available for {name}, using raw predictions")
                    except Exception as scaler_error:
                        logger.warning(f"Error inverse transforming predictions for {name}: {str(scaler_error)}")
                        # If scaler fails, use predictions as-is
                        logger.info(f"Using raw predictions for {name} (scaler not available)")

                if pred is not None:
                    base_predictions.append(pred)

            except Exception as e:
                logger.warning(f"Error making predictions with {name}: {str(e)}")

        if not base_predictions:
            logger.warning("No base predictions available, returning zeros")
            return np.zeros(n_periods)

        # If we have a meta-model, use it to combine predictions
        if self.meta_model is not None and len(base_predictions) >= 2:
            try:
                X_meta = np.column_stack(base_predictions)

                # Add exogenous variables if available and if they were used during training
                if exog is not None and hasattr(self, 'use_pca'):
                    try:
                        # Process exogenous variables consistently
                        exog_processed_meta = self._process_exogenous_variables(exog, fit_transform=False)
                        if exog_processed_meta is not None:
                            X_meta = np.column_stack([X_meta, exog_processed_meta])
                            logger.debug(f"Added exogenous variables to meta-model input: {X_meta.shape}")
                    except Exception as exog_meta_error:
                        logger.warning(f"Error processing exogenous variables for meta-model: {str(exog_meta_error)}. Skipping exogenous variables.")
                        # Continue without exogenous variables for meta-model

                # Check if the feature dimensions match what the meta-model expects
                expected_features = getattr(self.meta_model, 'n_features_in_', None)
                if expected_features is not None and X_meta.shape[1] != expected_features:
                    logger.warning(f"Feature dimension mismatch: X has {X_meta.shape[1]} features, but meta-model expects {expected_features}")

                    # Try to adjust feature dimensions
                    if X_meta.shape[1] < expected_features:
                        # Pad with zeros if we have fewer features
                        padding = np.zeros((X_meta.shape[0], expected_features - X_meta.shape[1]))
                        X_meta = np.column_stack([X_meta, padding])
                        logger.info(f"Padded features from {X_meta.shape[1] - padding.shape[1]} to {X_meta.shape[1]}")
                    elif X_meta.shape[1] > expected_features:
                        # Truncate if we have more features
                        X_meta = X_meta[:, :expected_features]
                        logger.info(f"Truncated features to {expected_features}")

                    # Check again after adjustment
                    if X_meta.shape[1] != expected_features:
                        logger.warning("Feature dimension adjustment failed, falling back to weighted average")
                        return self._fallback_prediction(base_predictions)

                # Make final predictions
                final_predictions = self.meta_model.predict(X_meta)
                logger.info(f"Made ensemble predictions using {getattr(self, 'meta_model_name', 'meta-model')}")
                return final_predictions
            except Exception as e:
                logger.warning(f"Error using meta-model: {str(e)}")
                fallback_result = self._fallback_prediction(base_predictions)
                if fallback_result is not None:
                    return fallback_result
                else:
                    logger.warning("Meta-model fallback returned None, returning zeros")
                    return np.zeros(n_periods)
        else:
            # Meta-model not available or insufficient base predictions
            logger.info("Meta-model not available, using fallback prediction")
            fallback_result = self._fallback_prediction(base_predictions)
            if fallback_result is not None:
                return fallback_result
            else:
                logger.warning("Fallback prediction returned None, returning zeros")
                return np.zeros(n_periods)
