#!/usr/bin/env python
"""
Ensemble ARIMA Model

This module implements an ensemble of ARIMA models for improved time series forecasting.
"""

import os
import numpy as np
import pandas as pd
from pathlib import Path
import pickle
import logging
from typing import Dict, List, Optional, Tuple, Any, Union
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from pmdarima import auto_arima
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import ElasticNet
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Configure logging
logger = logging.getLogger(__name__)

class EnsembleARIMAModel:
    """
    Ensemble ARIMA model that combines multiple ARIMA models with different configurations.
    """

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the ensemble ARIMA model.

        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.models = []
        self.meta_model = None
        self.scaler = StandardScaler()
        self.feature_scaler = StandardScaler()

    def build(self):
        """
        Build the ensemble model.
        """
        logger.info("Building Ensemble ARIMA model")

        # The actual building happens during training
        # This method is included for API compatibility
        pass

    def train(self, train_data: np.ndarray, exog: Optional[np.ndarray] = None):
        """
        Train the ensemble model.

        Args:
            train_data: Training data
            exog: Exogenous variables (optional)
        """
        logger.info("Training Ensemble ARIMA model")

        # Get configuration parameters
        n_models = self.config.get('n_models', 5)
        d = self.config.get('d', 1)
        seasonal = self.config.get('seasonal', False)
        seasonal_m = self.config.get('seasonal_m', 12)

        # Ensure train_data is a 1D array
        if isinstance(train_data, pd.Series):
            train_data = train_data.values

        if len(train_data.shape) > 1 and train_data.shape[1] > 1:
            train_data = train_data.flatten()

        # Scale the training data
        train_data_scaled = self.scaler.fit_transform(train_data.reshape(-1, 1)).flatten()

        # Scale exogenous variables if provided
        exog_scaled = None
        if exog is not None:
            # Ensure exog has the same number of samples as train_data
            if len(exog) != len(train_data_scaled):
                logger.warning(f"Exog shape {exog.shape} doesn't match train_data shape {train_data_scaled.shape}")
                # Trim exog to match train_data length
                if len(exog) > len(train_data_scaled):
                    logger.info(f"Trimming exog from {len(exog)} to {len(train_data_scaled)} samples")
                    exog = exog[:len(train_data_scaled)]
                else:
                    logger.warning("Exog has fewer samples than train_data, cannot use exogenous variables")
                    exog = None

            if exog is not None:
                exog_scaled = self.feature_scaler.fit_transform(exog)
                logger.info(f"Using exogenous variables with shape {exog_scaled.shape}")

        # Train multiple ARIMA models with different configurations
        self.models = []

        # Model 1: Best-performing ARIMA(5,d,5) - based on previous results
        try:
            model1 = ARIMA(
                train_data_scaled,
                order=(5, d, 5),
                exog=exog_scaled
            ).fit()  # Remove method parameter
            self.models.append(('arima_5_d_5', model1))
            logger.info("Trained ARIMA(5,d,5) model - best performing configuration")
        except Exception as e:
            logger.warning(f"Failed to train ARIMA(5,d,5): {str(e)}")

        # Model 2: ARIMA(2,d,2) - balanced model
        try:
            model2 = ARIMA(
                train_data_scaled,
                order=(2, d, 2),
                exog=exog_scaled
            ).fit()
            self.models.append(('arima_2_d_2', model2))
            logger.info("Trained ARIMA(2,d,2) model")
        except Exception as e:
            logger.warning(f"Failed to train ARIMA(2,d,2): {str(e)}")

        # Model 3: ARIMA(5,d,0) - AR focused
        try:
            model3 = ARIMA(
                train_data_scaled,
                order=(5, d, 0),
                exog=exog_scaled
            ).fit()
            self.models.append(('arima_5_d_0', model3))
            logger.info("Trained ARIMA(5,d,0) model")
        except Exception as e:
            logger.warning(f"Failed to train ARIMA(5,d,0): {str(e)}")

        # Model 4: ARIMA(0,d,5) - MA focused
        try:
            model4 = ARIMA(
                train_data_scaled,
                order=(0, d, 5),
                exog=exog_scaled
            ).fit()
            self.models.append(('arima_0_d_5', model4))
            logger.info("Trained ARIMA(0,d,5) model")
        except Exception as e:
            logger.warning(f"Failed to train ARIMA(0,d,5): {str(e)}")

        # Model 5: Auto ARIMA with improved settings
        try:
            model5 = auto_arima(
                train_data_scaled,
                start_p=1,
                start_q=1,
                max_p=5,
                max_q=5,
                d=d,
                seasonal=seasonal,
                m=seasonal_m if seasonal else 1,
                exogenous=exog_scaled,
                stepwise=False,
                random=True,
                random_state=42,
                information_criterion='aic',  # Use AIC for model selection
                with_intercept=True,  # Include intercept term
                max_order=10  # Limit total order to prevent overfitting
                # Remove method, n_fits and maxiter parameters
            )
            self.models.append(('auto_arima', model5))
            logger.info(f"Trained Auto ARIMA model with order {model5.order}")
        except Exception as e:
            logger.warning(f"Failed to train Auto ARIMA: {str(e)}")

        # Model 6: ARIMA(4,d,2) - another good configuration
        try:
            model6 = ARIMA(
                train_data_scaled,
                order=(4, d, 2),
                exog=exog_scaled
            ).fit()
            self.models.append(('arima_4_d_2', model6))
            logger.info("Trained ARIMA(4,d,2) model")
        except Exception as e:
            logger.warning(f"Failed to train ARIMA(4,d,2): {str(e)}")

        # Model 7: ARIMA(3,d,3) - balanced model
        try:
            model7 = ARIMA(
                train_data_scaled,
                order=(3, d, 3),
                exog=exog_scaled
            ).fit()
            self.models.append(('arima_3_d_3', model7))
            logger.info("Trained ARIMA(3,d,3) model")
        except Exception as e:
            logger.warning(f"Failed to train ARIMA(3,d,3): {str(e)}")

        # Train meta-model if we have enough base models
        if len(self.models) >= 2:
            self._train_meta_model(train_data, exog)

        logger.info(f"Trained {len(self.models)} ARIMA models for ensemble")

    def _train_meta_model(self, train_data: np.ndarray, exog: Optional[np.ndarray] = None):
        """
        Train a meta-model to combine predictions from base models.

        Args:
            train_data: Training data
            exog: Exogenous variables (optional)
        """
        # Get predictions from all base models
        base_predictions = []
        model_names = []

        for name, model in self.models:
            try:
                if hasattr(model, 'predict_in_sample'):
                    pred = model.predict_in_sample()
                elif hasattr(model, 'fittedvalues'):
                    pred = model.fittedvalues
                else:
                    # For auto_arima
                    pred = model.predict_in_sample()

                # Inverse transform if needed
                if name != 'auto_arima':  # statsmodels ARIMA
                    pred = self.scaler.inverse_transform(pred.reshape(-1, 1)).flatten()

                base_predictions.append(pred)
                model_names.append(name)
                logger.info(f"Added {name} predictions to meta-model training data")
            except Exception as e:
                logger.warning(f"Error getting in-sample predictions for {name}: {str(e)}")

        if not base_predictions:
            logger.warning("No base predictions available for meta-model training")
            return

        # Prepare meta-model training data
        X_meta = np.column_stack(base_predictions)

        # Calculate model weights based on individual performance
        model_weights = []
        for i, pred in enumerate(base_predictions):
            # Calculate MSE for this model
            mse = np.mean((train_data - pred) ** 2)
            # Convert to weight (lower MSE = higher weight)
            weight = 1.0 / (mse + 1e-10)  # Add small epsilon to avoid division by zero
            model_weights.append(weight)
            logger.info(f"Model {model_names[i]} MSE: {mse:.2f}, Weight: {weight:.6f}")

        # Normalize weights to sum to 1
        total_weight = sum(model_weights)
        model_weights = [w / total_weight for w in model_weights]
        logger.info(f"Normalized model weights: {[f'{w:.4f}' for w in model_weights]}")

        # Store model weights for weighted averaging fallback
        self.model_weights = model_weights

        # Add exogenous variables if available
        if exog is not None:
            # Use only a subset of exogenous variables to avoid overfitting
            if exog.shape[1] > 10:
                # Use PCA to reduce dimensionality
                from sklearn.decomposition import PCA
                # Calculate the appropriate number of components
                n_components = min(10, min(exog.shape[0], exog.shape[1]))

                # Ensure we have enough data for PCA
                if n_components > 0 and exog.shape[0] > 1:
                    logger.info(f"Using {n_components} PCA components based on data shape {exog.shape}")
                    try:
                        pca = PCA(n_components=n_components)
                        exog_reduced = pca.fit_transform(exog)
                        logger.info(f"Reduced exogenous variables from {exog.shape[1]} to {n_components} dimensions using PCA")
                        X_meta = np.column_stack([X_meta, exog_reduced])
                    except Exception as pca_error:
                        logger.warning(f"PCA failed: {pca_error}. Using original exogenous variables.")
                        X_meta = np.column_stack([X_meta, exog])
                else:
                    logger.warning(f"Insufficient data for PCA (shape: {exog.shape}). Using original exogenous variables.")
                    X_meta = np.column_stack([X_meta, exog])
            else:
                X_meta = np.column_stack([X_meta, exog])

        # Try different meta-models and select the best one
        from sklearn.model_selection import TimeSeriesSplit
        from sklearn.metrics import mean_squared_error

        # Define candidate meta-models
        from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
        from sklearn.linear_model import ElasticNet, Ridge

        meta_models = [
            ('gradient_boosting', GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, max_depth=3, random_state=42)),
            ('random_forest', RandomForestRegressor(n_estimators=100, max_depth=5, random_state=42)),
            ('extra_trees', ExtraTreesRegressor(n_estimators=100, max_depth=5, random_state=42)),
            ('elastic_net', ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42)),
            ('ridge', Ridge(alpha=1.0, random_state=42))
        ]

        # Use time series cross-validation to evaluate meta-models
        tscv = TimeSeriesSplit(n_splits=5)
        best_score = float('inf')
        best_model = None
        best_model_name = None

        for name, model in meta_models:
            try:
                scores = []
                for train_idx, test_idx in tscv.split(X_meta):
                    X_train, X_test = X_meta[train_idx], X_meta[test_idx]
                    y_train, y_test = train_data[train_idx], train_data[test_idx]

                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_test)
                    score = mean_squared_error(y_test, y_pred)
                    scores.append(score)

                avg_score = np.mean(scores)
                logger.info(f"Meta-model {name} average MSE: {avg_score:.2f}")

                if avg_score < best_score:
                    best_score = avg_score
                    best_model = model
                    best_model_name = name
            except Exception as e:
                logger.warning(f"Error evaluating meta-model {name}: {str(e)}")

        if best_model is not None:
            logger.info(f"Selected {best_model_name} as the best meta-model with MSE: {best_score:.2f}")
            # Train the best model on all data
            best_model.fit(X_meta, train_data)
            self.meta_model = best_model
            self.meta_model_name = best_model_name
        else:
            # Fallback to gradient boosting if no model was selected
            logger.warning("No meta-model was selected, falling back to gradient boosting")
            self.meta_model = GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, max_depth=3, random_state=42)
            self.meta_model.fit(X_meta, train_data)
            self.meta_model_name = "gradient_boosting"

        logger.info(f"Trained {self.meta_model_name} meta-model for ensemble combination")

    def predict(self, n_periods: int, exog: Optional[np.ndarray] = None, X: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Make predictions with the ensemble model.

        Args:
            n_periods: Number of periods to predict
            exog: Exogenous variables (optional)
            X: Alternative name for exogenous variables (for compatibility with auto_arima)

        Returns:
            Predictions
        """
        # Handle X parameter for compatibility with auto_arima
        if exog is None and X is not None:
            exog = X

        if not self.models:
            logger.warning("No models in ensemble, returning zeros")
            return np.zeros(n_periods)

        # Scale exogenous variables if provided
        exog_scaled = None
        if exog is not None:
            try:
                # Check if exog has the right shape
                if len(exog) != n_periods:
                    logger.warning(f"Exog shape {exog.shape} doesn't match n_periods {n_periods}")
                    # Try to adjust exog to match n_periods
                    if len(exog) > n_periods:
                        logger.info(f"Trimming exog from {len(exog)} to {n_periods} samples")
                        exog = exog[:n_periods]
                    else:
                        logger.warning("Exog has fewer samples than n_periods, cannot use exogenous variables")
                        exog = None

                if exog is not None:
                    # Check if the number of features matches what the scaler expects
                    expected_features = self.feature_scaler.n_features_in_ if hasattr(self.feature_scaler, 'n_features_in_') else None

                    # Handle different exog shapes
                    if len(exog.shape) == 3:
                        # Reshape from (batch, seq, features) to (batch, features)
                        exog = exog.reshape(exog.shape[0], -1)
                        logger.info(f"Reshaped exog from 3D to 2D: {exog.shape}")
                    elif len(exog.shape) == 1:
                        # Single sample, reshape to (1, features)
                        exog = exog.reshape(1, -1)

                    actual_features = exog.shape[1] if len(exog.shape) > 1 else exog.shape[0]

                    if expected_features is not None and actual_features != expected_features:
                        # Try to adjust features to match expected dimensions
                        if actual_features > expected_features:
                            # Truncate excess features
                            exog = exog[:, :expected_features]
                            logger.info(f"Truncated exog features from {actual_features} to {expected_features}")
                            exog_scaled = self.feature_scaler.transform(exog)
                        elif actual_features < expected_features:
                            # Pad with zeros for missing features
                            padding = np.zeros((exog.shape[0], expected_features - actual_features))
                            exog = np.column_stack([exog, padding])
                            logger.info(f"Padded exog features from {actual_features} to {expected_features}")
                            exog_scaled = self.feature_scaler.transform(exog)
                        else:
                            logger.warning(f"Feature dimension mismatch: expected {expected_features}, got {actual_features}. Disabling exogenous variables.")
                            exog_scaled = None
                    else:
                        exog_scaled = self.feature_scaler.transform(exog)
                        logger.info(f"Using exogenous variables with shape {exog_scaled.shape} for prediction")
            except Exception as e:
                logger.warning(f"Error scaling exogenous variables: {str(e)}")
                exog_scaled = None

        # Get predictions from all base models
        base_predictions = []
        for name, model in self.models:
            try:
                # Try with exogenous variables first, then fallback to without
                pred = None

                if name == 'auto_arima':
                    # For auto_arima
                    if exog_scaled is not None:
                        try:
                            pred = model.predict(n_periods=n_periods, X=exog_scaled)
                            logger.info(f"Made predictions with {name} model using exogenous variables")
                        except Exception as exog_error:
                            logger.warning(f"Error making predictions with {name} using exogenous variables: {str(exog_error)}")
                            pred = model.predict(n_periods=n_periods)
                            logger.info(f"Made predictions with {name} model without exogenous variables")
                    else:
                        pred = model.predict(n_periods=n_periods)
                        logger.info(f"Made predictions with {name} model")
                else:
                    # For statsmodels ARIMA/SARIMAX
                    if exog_scaled is not None:
                        try:
                            pred = model.forecast(steps=n_periods, exog=exog_scaled)
                            logger.info(f"Made predictions with {name} model using exogenous variables")
                        except Exception as exog_error:
                            logger.warning(f"Error making predictions with {name} using exogenous variables: {str(exog_error)}")
                            pred = model.forecast(steps=n_periods)
                            logger.info(f"Made predictions with {name} model without exogenous variables")
                    else:
                        pred = model.forecast(steps=n_periods)
                        logger.info(f"Made predictions with {name} model")

                # Inverse transform predictions
                if name != 'auto_arima' and pred is not None:  # statsmodels ARIMA
                    pred = self.scaler.inverse_transform(pred.reshape(-1, 1)).flatten()

                if pred is not None:
                    base_predictions.append(pred)

            except Exception as e:
                logger.warning(f"Error making predictions with {name}: {str(e)}")

        if not base_predictions:
            logger.warning("No base predictions available, returning zeros")
            return np.zeros(n_periods)

        # If we have a meta-model, use it to combine predictions
        if self.meta_model is not None and len(base_predictions) >= 2:
            try:
                X_meta = np.column_stack(base_predictions)

                # Add exogenous variables if available
                if exog is not None:
                    # Use only a subset of exogenous variables to avoid overfitting
                    if exog.shape[1] > 10:
                        # Use PCA to reduce dimensionality
                        from sklearn.decomposition import PCA
                        # Calculate the appropriate number of components
                        n_components = min(10, min(exog.shape[0], exog.shape[1]))

                        # Ensure we have enough data for PCA
                        if n_components > 0 and exog.shape[0] > 1:
                            logger.info(f"Using {n_components} PCA components based on data shape {exog.shape}")
                            try:
                                pca = PCA(n_components=n_components)
                                exog_reduced = pca.fit_transform(exog)
                                logger.info(f"Reduced exogenous variables from {exog.shape[1]} to {n_components} dimensions using PCA")
                                X_meta = np.column_stack([X_meta, exog_reduced])
                            except Exception as pca_error:
                                logger.warning(f"PCA failed: {pca_error}. Using original exogenous variables.")
                                X_meta = np.column_stack([X_meta, exog])
                        else:
                            logger.warning(f"Insufficient data for PCA (shape: {exog.shape}). Using original exogenous variables.")
                            X_meta = np.column_stack([X_meta, exog])
                    else:
                        X_meta = np.column_stack([X_meta, exog])

                # Check if the feature dimensions match what the meta-model expects
                expected_features = getattr(self.meta_model, 'n_features_in_', None)
                if expected_features is not None and X_meta.shape[1] != expected_features:
                    logger.warning(f"Feature dimension mismatch: X has {X_meta.shape[1]} features, but meta-model expects {expected_features}")

                    # Try to adjust feature dimensions
                    if X_meta.shape[1] < expected_features:
                        # Pad with zeros if we have fewer features
                        padding = np.zeros((X_meta.shape[0], expected_features - X_meta.shape[1]))
                        X_meta = np.column_stack([X_meta, padding])
                        logger.info(f"Padded features from {X_meta.shape[1] - padding.shape[1]} to {X_meta.shape[1]}")
                    elif X_meta.shape[1] > expected_features:
                        # Truncate if we have more features
                        X_meta = X_meta[:, :expected_features]
                        logger.info(f"Truncated features to {expected_features}")

                    # Check again after adjustment
                    if X_meta.shape[1] != expected_features:
                        logger.info("Falling back to weighted average due to feature dimension mismatch")
                        if hasattr(self, 'model_weights') and len(self.model_weights) == len(base_predictions):
                            weighted_predictions = np.zeros_like(base_predictions[0])
                            for i, pred in enumerate(base_predictions):
                                weighted_predictions += self.model_weights[i] * pred
                            logger.info("Using weighted average for predictions")
                            return weighted_predictions
                        else:
                            # Simple average as final fallback
                            simple_average = np.mean(base_predictions, axis=0)
                            logger.info("Using simple average for predictions")
                            return simple_average

                # Make final predictions
                final_predictions = self.meta_model.predict(X_meta)
                logger.info(f"Made ensemble predictions using {getattr(self, 'meta_model_name', 'meta-model')}")
                return final_predictions
            except Exception as e:
                logger.warning(f"Error using meta-model: {str(e)}")
                # Fall back to weighted average if available
                if hasattr(self, 'model_weights') and len(self.model_weights) == len(base_predictions):
                    weighted_predictions = np.zeros_like(base_predictions[0])
                    for i, pred in enumerate(base_predictions):
                        weighted_predictions += self.model_weights[i] * pred
                    logger.info("Falling back to weighted average for predictions")
                    return weighted_predictions
                else:
                    # Fall back to simple average
                    final_predictions = np.mean(base_predictions, axis=0)
                    logger.info("Falling back to simple average for predictions")
                    return final_predictions
        elif hasattr(self, 'model_weights') and len(self.model_weights) == len(base_predictions):
            # Use weighted average if meta-model is not available but weights are
            weighted_predictions = np.zeros_like(base_predictions[0])
            for i, pred in enumerate(base_predictions):
                weighted_predictions += self.model_weights[i] * pred
            logger.info("Made ensemble predictions using weighted average")
            return weighted_predictions
        else:
            # Simple average of base predictions
            final_predictions = np.mean(base_predictions, axis=0)
            logger.info("Made ensemble predictions using simple average")
            return final_predictions
