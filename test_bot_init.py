#!/usr/bin/env python3
"""
Test script to verify bot initialization without launching MT5 terminals.
"""

import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.absolute()
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_configuration():
    """Test configuration loading."""
    try:
        logger.info("Testing configuration loading...")
        
        # Test UnifiedConfigManager
        from config.unified_config import config_manager
        config = config_manager.get_config()
        logger.info(f"UnifiedConfigManager: {config is not None}")
        
        # Test ConfigurationService
        from config.service import ConfigurationService
        cs = ConfigurationService()
        config2 = cs.get_config()
        logger.info(f"ConfigurationService: {config2 is not None}")
        
        # Test they return the same object
        logger.info(f"Same config object: {config is config2}")
        
        return True
        
    except Exception as e:
        logger.error(f"Configuration test failed: {str(e)}")
        return False

def test_bot_initialization():
    """Test bot initialization without MT5."""
    try:
        logger.info("Testing bot initialization...")
        
        # Import required modules
        from config.unified_config import config_manager
        from utils.enhanced_error_handler import EnhancedErrorHandler
        from utils.thread_manager import ThreadManager
        from utils.mt5.mt5_connection_manager import MT5ConnectionManager
        from trading.bot import TradingBot
        
        # Create managers
        error_handler = EnhancedErrorHandler()
        thread_manager = ThreadManager(max_workers=4, thread_name_prefix="Test")
        mt5_manager = MT5ConnectionManager(config_manager)
        
        # Try to create a bot instance
        logger.info("Creating TradingBot instance...")
        bot = TradingBot(
            config_manager=config_manager,
            error_handler=error_handler,
            thread_manager=thread_manager,
            mt5_manager=mt5_manager,
            terminal_id="1",
            visualizer=None,
            memory_manager=None
        )
        
        logger.info("TradingBot created successfully!")
        logger.info(f"Bot terminal ID: {bot.terminal_id}")
        logger.info(f"Bot primary timeframe: {bot.primary_timeframe}")
        
        return True
        
    except Exception as e:
        logger.error(f"Bot initialization test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    logger.info("Starting bot initialization tests...")
    
    # Test configuration
    config_ok = test_configuration()
    if not config_ok:
        logger.error("Configuration test failed!")
        return 1
    
    # Test bot initialization
    bot_ok = test_bot_initialization()
    if not bot_ok:
        logger.error("Bot initialization test failed!")
        return 1
    
    logger.info("All tests passed!")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
