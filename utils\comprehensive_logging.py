#!/usr/bin/env python3
"""
Comprehensive Logging and Monitoring System

This module provides a comprehensive logging system with:
- Separate logs for each terminal
- Readable main.log with structured format
- Performance tracking per terminal
- Real-time monitoring capabilities
- Log rotation and management
"""

import os
import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional, List
import json
from datetime import datetime, timedelta
import threading
from dataclasses import dataclass, asdict
import time

@dataclass
class TerminalMetrics:
    """Metrics for a specific terminal."""
    terminal_id: str
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_profit: float = 0.0
    current_balance: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    last_trade_time: Optional[str] = None
    last_signal: Optional[str] = None
    active_positions: int = 0
    errors_count: int = 0
    warnings_count: int = 0
    status: str = "INACTIVE"
    last_update: str = ""

class ComprehensiveLogger:
    """Comprehensive logging system for trading bot operations."""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        self.terminals_dir = self.log_dir / "terminals"
        self.performance_dir = self.log_dir / "performance"
        self.monitoring_dir = self.log_dir / "monitoring"
        
        for dir_path in [self.terminals_dir, self.performance_dir, self.monitoring_dir]:
            dir_path.mkdir(exist_ok=True)
        
        # Terminal metrics tracking
        self.terminal_metrics: Dict[str, TerminalMetrics] = {}
        self.metrics_lock = threading.RLock()
        
        # Logger instances
        self.loggers: Dict[str, logging.Logger] = {}
        self.handlers: Dict[str, List[logging.Handler]] = {}
        
        # Setup main logger
        self._setup_main_logger()
        
        # Setup monitoring
        self.monitoring_active = False
        self.monitoring_thread = None
        
    def _setup_main_logger(self) -> None:
        """Setup the main readable log file."""
        main_logger = logging.getLogger("main")
        main_logger.setLevel(logging.INFO)
        
        # Clear existing handlers
        main_logger.handlers.clear()
        
        # Main log file with rotation
        main_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "main.log",
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10,
            encoding='utf-8'
        )
        
        # Readable format for main log
        main_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        main_handler.setFormatter(main_formatter)
        main_logger.addHandler(main_handler)
        
        # Console handler for main logger
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        main_logger.addHandler(console_handler)
        
        self.loggers["main"] = main_logger
        self.handlers["main"] = [main_handler, console_handler]
        
    def setup_terminal_logger(self, terminal_id: str) -> logging.Logger:
        """Setup logging for a specific terminal."""
        logger_name = f"terminal_{terminal_id}"
        
        if logger_name in self.loggers:
            return self.loggers[logger_name]
        
        # Create terminal logger
        terminal_logger = logging.getLogger(logger_name)
        terminal_logger.setLevel(logging.INFO)
        terminal_logger.handlers.clear()
        
        # Terminal-specific log file
        terminal_log_file = self.terminals_dir / f"terminal_{terminal_id}.log"
        terminal_handler = logging.handlers.RotatingFileHandler(
            terminal_log_file,
            maxBytes=20*1024*1024,  # 20MB
            backupCount=5,
            encoding='utf-8'
        )
        
        # Detailed format for terminal logs (remove terminal_id from format to avoid KeyError)
        terminal_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | [T' + str(terminal_id) + '] | %(funcName)-20s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        terminal_handler.setFormatter(terminal_formatter)
        terminal_logger.addHandler(terminal_handler)
        
        # Performance log for this terminal
        perf_log_file = self.performance_dir / f"terminal_{terminal_id}_performance.log"
        perf_handler = logging.handlers.RotatingFileHandler(
            perf_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=3,
            encoding='utf-8'
        )
        
        perf_formatter = logging.Formatter(
            '%(asctime)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        perf_handler.setFormatter(perf_formatter)
        
        # Create performance logger
        perf_logger = logging.getLogger(f"performance_{terminal_id}")
        perf_logger.setLevel(logging.INFO)
        perf_logger.handlers.clear()
        perf_logger.addHandler(perf_handler)
        
        self.loggers[logger_name] = terminal_logger
        self.loggers[f"performance_{terminal_id}"] = perf_logger
        self.handlers[logger_name] = [terminal_handler]
        self.handlers[f"performance_{terminal_id}"] = [perf_handler]
        
        # Initialize terminal metrics
        with self.metrics_lock:
            if terminal_id not in self.terminal_metrics:
                self.terminal_metrics[terminal_id] = TerminalMetrics(
                    terminal_id=terminal_id,
                    last_update=datetime.now().isoformat()
                )
        
        return terminal_logger
    
    def get_terminal_logger(self, terminal_id: str) -> logging.Logger:
        """Get logger for a specific terminal."""
        logger_name = f"terminal_{terminal_id}"
        if logger_name not in self.loggers:
            return self.setup_terminal_logger(terminal_id)
        return self.loggers[logger_name]
    
    def get_performance_logger(self, terminal_id: str) -> logging.Logger:
        """Get performance logger for a specific terminal."""
        logger_name = f"performance_{terminal_id}"
        if logger_name not in self.loggers:
            self.setup_terminal_logger(terminal_id)  # This creates both loggers
        return self.loggers[logger_name]
    
    def get_main_logger(self) -> logging.Logger:
        """Get the main logger."""
        return self.loggers["main"]
    
    def log_trade_execution(self, terminal_id: str, trade_data: Dict[str, Any]) -> None:
        """Log trade execution with comprehensive details."""
        terminal_logger = self.get_terminal_logger(terminal_id)
        perf_logger = self.get_performance_logger(terminal_id)
        main_logger = self.get_main_logger()
        
        # Extract trade details
        action = trade_data.get('action', 'unknown')
        symbol = trade_data.get('symbol', 'unknown')
        volume = trade_data.get('volume', 0.0)
        price = trade_data.get('price', 0.0)
        profit = trade_data.get('profit_loss', 0.0)
        ticket = trade_data.get('ticket', 'unknown')
        
        # Log to terminal-specific log
        terminal_logger.info(
            f"TRADE_EXECUTED | Action: {action.upper()} | Symbol: {symbol} | "
            f"Volume: {volume} | Price: {price} | Ticket: {ticket} | P&L: {profit:.2f}",
            extra={'terminal_id': terminal_id}
        )
        
        # Log to performance log
        perf_logger.info(
            f"TRADE | {action.upper()} | {symbol} | Vol: {volume} | "
            f"Price: {price} | P&L: {profit:.2f} | Ticket: {ticket}"
        )
        
        # Log to main log (summary)
        main_logger.info(
            f"[T{terminal_id}] Trade executed: {action.upper()} {volume} {symbol} "
            f"at {price} (P&L: {profit:.2f})"
        )
        
        # Update metrics
        self.update_terminal_metrics(terminal_id, trade_data)
    
    def log_signal_generation(self, terminal_id: str, signal_data: Dict[str, Any]) -> None:
        """Log signal generation details."""
        terminal_logger = self.get_terminal_logger(terminal_id)
        main_logger = self.get_main_logger()
        
        signal_type = signal_data.get('direction', 'unknown')
        confidence = signal_data.get('confidence', 0.0)
        symbol = signal_data.get('symbol', 'unknown')
        
        terminal_logger.info(
            f"SIGNAL_GENERATED | Type: {signal_type} | Symbol: {symbol} | "
            f"Confidence: {confidence:.3f} | Models: {signal_data.get('models_used', [])}",
            extra={'terminal_id': terminal_id}
        )
        
        main_logger.info(
            f"[T{terminal_id}] Signal: {signal_type} for {symbol} "
            f"(confidence: {confidence:.3f})"
        )
        
        # Update metrics
        with self.metrics_lock:
            if terminal_id in self.terminal_metrics:
                self.terminal_metrics[terminal_id].last_signal = signal_type
                self.terminal_metrics[terminal_id].last_update = datetime.now().isoformat()
    
    def log_error(self, terminal_id: str, error_msg: str, error_type: str = "ERROR") -> None:
        """Log errors with proper categorization."""
        terminal_logger = self.get_terminal_logger(terminal_id)
        main_logger = self.get_main_logger()
        
        terminal_logger.error(
            f"{error_type} | {error_msg}",
            extra={'terminal_id': terminal_id}
        )
        
        main_logger.error(f"[T{terminal_id}] {error_type}: {error_msg}")
        
        # Update error count
        with self.metrics_lock:
            if terminal_id in self.terminal_metrics:
                self.terminal_metrics[terminal_id].errors_count += 1
                self.terminal_metrics[terminal_id].last_update = datetime.now().isoformat()
    
    def log_warning(self, terminal_id: str, warning_msg: str) -> None:
        """Log warnings."""
        terminal_logger = self.get_terminal_logger(terminal_id)
        main_logger = self.get_main_logger()
        
        terminal_logger.warning(
            f"WARNING | {warning_msg}",
            extra={'terminal_id': terminal_id}
        )
        
        main_logger.warning(f"[T{terminal_id}] WARNING: {warning_msg}")
        
        # Update warning count
        with self.metrics_lock:
            if terminal_id in self.terminal_metrics:
                self.terminal_metrics[terminal_id].warnings_count += 1
                self.terminal_metrics[terminal_id].last_update = datetime.now().isoformat()
    
    def update_terminal_metrics(self, terminal_id: str, data: Dict[str, Any]) -> None:
        """Update metrics for a terminal."""
        with self.metrics_lock:
            if terminal_id not in self.terminal_metrics:
                self.terminal_metrics[terminal_id] = TerminalMetrics(terminal_id=terminal_id)
            
            metrics = self.terminal_metrics[terminal_id]
            
            # Update trade counts
            if 'action' in data:
                metrics.total_trades += 1
                metrics.last_trade_time = datetime.now().isoformat()
                
                profit = data.get('profit_loss', 0.0)
                metrics.total_profit += profit
                
                if profit > 0:
                    metrics.winning_trades += 1
                elif profit < 0:
                    metrics.losing_trades += 1
                
                # Calculate win rate
                if metrics.total_trades > 0:
                    metrics.win_rate = (metrics.winning_trades / metrics.total_trades) * 100
            
            # Update other metrics
            if 'balance' in data:
                metrics.current_balance = data['balance']
            
            if 'drawdown' in data:
                metrics.max_drawdown = max(metrics.max_drawdown, data['drawdown'])
            
            if 'active_positions' in data:
                metrics.active_positions = data['active_positions']
            
            if 'status' in data:
                metrics.status = data['status']
            
            metrics.last_update = datetime.now().isoformat()
    
    def get_terminal_metrics(self, terminal_id: str) -> Optional[TerminalMetrics]:
        """Get metrics for a specific terminal."""
        with self.metrics_lock:
            return self.terminal_metrics.get(terminal_id)
    
    def get_all_metrics(self) -> Dict[str, TerminalMetrics]:
        """Get metrics for all terminals."""
        with self.metrics_lock:
            return self.terminal_metrics.copy()
    
    def save_metrics_snapshot(self) -> None:
        """Save current metrics to file."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        metrics_file = self.monitoring_dir / f"metrics_snapshot_{timestamp}.json"
        
        with self.metrics_lock:
            metrics_data = {
                'timestamp': datetime.now().isoformat(),
                'terminals': {
                    terminal_id: asdict(metrics)
                    for terminal_id, metrics in self.terminal_metrics.items()
                }
            }
        
        with open(metrics_file, 'w') as f:
            json.dump(metrics_data, f, indent=2, default=str)
    
    def start_monitoring(self, interval: int = 60) -> None:
        """Start background monitoring and metrics collection."""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        def monitoring_loop():
            while self.monitoring_active:
                try:
                    # Save metrics snapshot
                    self.save_metrics_snapshot()
                    
                    # Log summary to main log
                    main_logger = self.get_main_logger()
                    with self.metrics_lock:
                        active_terminals = len([
                            m for m in self.terminal_metrics.values()
                            if m.status == "ACTIVE"
                        ])
                        total_trades = sum(m.total_trades for m in self.terminal_metrics.values())
                        total_profit = sum(m.total_profit for m in self.terminal_metrics.values())
                    
                    main_logger.info(
                        f"MONITORING | Active Terminals: {active_terminals} | "
                        f"Total Trades: {total_trades} | Total P&L: {total_profit:.2f}"
                    )
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    main_logger.error(f"Error in monitoring loop: {str(e)}")
                    time.sleep(interval)
        
        self.monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
        self.monitoring_thread.start()
    
    def stop_monitoring(self) -> None:
        """Stop background monitoring."""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
    
    def cleanup(self) -> None:
        """Cleanup logging resources."""
        self.stop_monitoring()
        
        # Close all handlers
        for handlers_list in self.handlers.values():
            for handler in handlers_list:
                handler.close()
        
        # Clear loggers
        self.loggers.clear()
        self.handlers.clear()

# Global instance
comprehensive_logger = ComprehensiveLogger()

def get_terminal_logger(terminal_id: str) -> logging.Logger:
    """Get logger for a specific terminal."""
    return comprehensive_logger.get_terminal_logger(terminal_id)

def get_main_logger() -> logging.Logger:
    """Get the main logger."""
    return comprehensive_logger.get_main_logger()

def log_trade(terminal_id: str, trade_data: Dict[str, Any]) -> None:
    """Log trade execution."""
    comprehensive_logger.log_trade_execution(terminal_id, trade_data)

def log_signal(terminal_id: str, signal_data: Dict[str, Any]) -> None:
    """Log signal generation."""
    comprehensive_logger.log_signal_generation(terminal_id, signal_data)

def log_terminal_error(terminal_id: str, error_msg: str, error_type: str = "ERROR") -> None:
    """Log terminal-specific error."""
    comprehensive_logger.log_error(terminal_id, error_msg, error_type)

def log_terminal_warning(terminal_id: str, warning_msg: str) -> None:
    """Log terminal-specific warning."""
    comprehensive_logger.log_warning(terminal_id, warning_msg)

def update_metrics(terminal_id: str, data: Dict[str, Any]) -> None:
    """Update terminal metrics."""
    comprehensive_logger.update_terminal_metrics(terminal_id, data)

def start_monitoring(interval: int = 60) -> None:
    """Start monitoring system."""
    comprehensive_logger.start_monitoring(interval)

def stop_monitoring() -> None:
    """Stop monitoring system."""
    comprehensive_logger.stop_monitoring()
