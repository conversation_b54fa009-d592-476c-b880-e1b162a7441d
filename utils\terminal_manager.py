#!/usr/bin/env python3
"""
Terminal Manager

Ensures all configured terminals are actively trading and properly managed.
Provides comprehensive monitoring and control of multiple MT5 terminals.
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
import json

from utils.comprehensive_logging import get_main_logger, get_terminal_logger, update_metrics

@dataclass
class TerminalStatus:
    """Status information for a terminal."""
    terminal_id: str
    is_running: bool = False
    is_trading: bool = False
    last_activity: Optional[datetime] = None
    last_trade: Optional[datetime] = None
    error_count: int = 0
    consecutive_errors: int = 0
    status_message: str = "INACTIVE"
    bot_instance: Optional[object] = None
    connection_healthy: bool = False
    trades_today: int = 0
    last_signal: Optional[str] = None

class TerminalManager:
    """Manages multiple MT5 terminals and ensures they're all actively trading."""
    
    def __init__(self, config_manager=None, mt5_manager=None, error_handler=None):
        self.config_manager = config_manager
        self.mt5_manager = mt5_manager
        self.error_handler = error_handler
        
        # Terminal tracking
        self.terminals: Dict[str, TerminalStatus] = {}
        self.terminal_bots: Dict[str, object] = {}
        self.lock = threading.RLock()
        
        # Monitoring
        self.monitoring_active = False
        self.monitoring_thread = None
        self.check_interval = 120  # Check every 2 minutes (less aggressive)

        # Loggers
        self.main_logger = get_main_logger()

        # Configuration
        self.max_consecutive_errors = 10  # More lenient error threshold
        self.restart_delay = 180  # Wait 3 minutes before restarting failed terminal
        self.activity_timeout = 600  # 10 minutes without activity = inactive (more lenient)
        
    def initialize_terminals(self, terminal_ids: List[str]) -> bool:
        """Initialize all specified terminals."""
        self.main_logger.info(f"Initializing {len(terminal_ids)} terminals: {terminal_ids}")
        
        with self.lock:
            success_count = 0
            
            for terminal_id in terminal_ids:
                try:
                    # Initialize terminal status
                    self.terminals[terminal_id] = TerminalStatus(
                        terminal_id=terminal_id,
                        last_activity=datetime.now()
                    )
                    
                    # Get terminal logger
                    terminal_logger = get_terminal_logger(terminal_id)
                    terminal_logger.info(f"Initializing terminal {terminal_id}")
                    
                    # Update metrics
                    update_metrics(terminal_id, {
                        'status': 'INITIALIZING',
                        'terminal_id': terminal_id
                    })
                    
                    success_count += 1
                    self.main_logger.info(f"[T{terminal_id}] Terminal initialized successfully")
                    
                except Exception as e:
                    self.main_logger.error(f"[T{terminal_id}] Failed to initialize: {str(e)}")
                    if self.error_handler:
                        self.error_handler.handle_error(e, context={'terminal_id': terminal_id})
            
            self.main_logger.info(f"Successfully initialized {success_count}/{len(terminal_ids)} terminals")
            return success_count == len(terminal_ids)
    
    def register_bot(self, terminal_id: str, bot_instance: object) -> bool:
        """Register a trading bot instance for a terminal."""
        with self.lock:
            if terminal_id not in self.terminals:
                self.main_logger.error(f"[T{terminal_id}] Cannot register bot - terminal not initialized")
                return False
            
            self.terminals[terminal_id].bot_instance = bot_instance
            self.terminal_bots[terminal_id] = bot_instance
            
            terminal_logger = get_terminal_logger(terminal_id)
            terminal_logger.info(f"Trading bot registered for terminal {terminal_id}")
            
            # Update status
            self.terminals[terminal_id].status_message = "BOT_REGISTERED"
            self.terminals[terminal_id].last_activity = datetime.now()
            
            update_metrics(terminal_id, {
                'status': 'BOT_REGISTERED',
                'bot_registered': True
            })
            
            self.main_logger.info(f"[T{terminal_id}] Trading bot registered successfully")
            return True
    
    def start_terminal_trading(self, terminal_id: str) -> bool:
        """Start trading for a specific terminal."""
        with self.lock:
            if terminal_id not in self.terminals:
                self.main_logger.error(f"[T{terminal_id}] Cannot start trading - terminal not initialized")
                return False
            
            terminal_status = self.terminals[terminal_id]
            bot_instance = terminal_status.bot_instance
            
            if not bot_instance:
                self.main_logger.error(f"[T{terminal_id}] Cannot start trading - no bot registered")
                return False
            
            try:
                terminal_logger = get_terminal_logger(terminal_id)
                terminal_logger.info(f"Starting trading for terminal {terminal_id}")
                
                # Start the bot
                if hasattr(bot_instance, 'start'):
                    success = bot_instance.start()
                    if success:
                        terminal_status.is_trading = True
                        terminal_status.status_message = "TRADING_ACTIVE"
                        terminal_status.last_activity = datetime.now()
                        
                        update_metrics(terminal_id, {
                            'status': 'TRADING_ACTIVE',
                            'is_trading': True
                        })
                        
                        self.main_logger.info(f"[T{terminal_id}] Trading started successfully")
                        return True
                    else:
                        self.main_logger.error(f"[T{terminal_id}] Failed to start trading bot")
                        return False
                else:
                    self.main_logger.error(f"[T{terminal_id}] Bot instance has no start method")
                    return False
                    
            except Exception as e:
                self.main_logger.error(f"[T{terminal_id}] Error starting trading: {str(e)}")
                if self.error_handler:
                    self.error_handler.handle_error(e, context={'terminal_id': terminal_id})
                return False
    
    def start_all_trading(self) -> bool:
        """Start trading for all registered terminals."""
        self.main_logger.info("Starting trading for all terminals...")
        
        with self.lock:
            success_count = 0
            total_terminals = len(self.terminals)
            
            for terminal_id in self.terminals.keys():
                if self.start_terminal_trading(terminal_id):
                    success_count += 1
            
            self.main_logger.info(f"Successfully started trading on {success_count}/{total_terminals} terminals")
            
            # Start monitoring if any terminals are trading
            if success_count > 0:
                self.start_monitoring()
            
            return success_count == total_terminals
    
    def stop_terminal_trading(self, terminal_id: str) -> bool:
        """Stop trading for a specific terminal."""
        with self.lock:
            if terminal_id not in self.terminals:
                return False
            
            terminal_status = self.terminals[terminal_id]
            bot_instance = terminal_status.bot_instance
            
            if bot_instance and hasattr(bot_instance, 'stop'):
                try:
                    bot_instance.stop()
                    terminal_status.is_trading = False
                    terminal_status.status_message = "TRADING_STOPPED"
                    
                    update_metrics(terminal_id, {
                        'status': 'TRADING_STOPPED',
                        'is_trading': False
                    })
                    
                    self.main_logger.info(f"[T{terminal_id}] Trading stopped")
                    return True
                except Exception as e:
                    self.main_logger.error(f"[T{terminal_id}] Error stopping trading: {str(e)}")
                    return False
            
            return False
    
    def stop_all_trading(self) -> None:
        """Stop trading for all terminals."""
        self.main_logger.info("Stopping trading for all terminals...")
        
        with self.lock:
            for terminal_id in self.terminals.keys():
                self.stop_terminal_trading(terminal_id)
        
        self.stop_monitoring()
        self.main_logger.info("All terminal trading stopped")
    
    def check_terminal_health(self, terminal_id: str) -> bool:
        """Check if a terminal is healthy and trading properly."""
        with self.lock:
            if terminal_id not in self.terminals:
                return False

            terminal_status = self.terminals[terminal_id]
            now = datetime.now()

            # If bot instance exists and has is_running method, check it
            if terminal_status.bot_instance and hasattr(terminal_status.bot_instance, 'is_running'):
                if not terminal_status.bot_instance.is_running():
                    terminal_status.status_message = "BOT_STOPPED"
                    terminal_status.is_trading = False
                    return False
                else:
                    # Bot is running, update activity
                    terminal_status.last_activity = now
                    terminal_status.status_message = "TRADING_ACTIVE"
                    terminal_status.is_trading = True
                    return True

            # If no bot instance or no is_running method, check activity timeout
            if terminal_status.last_activity:
                time_since_activity = now - terminal_status.last_activity
                if time_since_activity > timedelta(seconds=self.activity_timeout):
                    terminal_status.status_message = "INACTIVE_TIMEOUT"
                    return False

            # Check error count (but be more lenient)
            if terminal_status.consecutive_errors >= self.max_consecutive_errors:
                terminal_status.status_message = "TOO_MANY_ERRORS"
                return False

            # If we reach here, terminal is considered healthy
            terminal_status.status_message = "HEALTHY"
            return True
    
    def restart_terminal(self, terminal_id: str) -> bool:
        """Restart trading for a failed terminal."""
        self.main_logger.info(f"[T{terminal_id}] Attempting to restart terminal...")
        
        try:
            # Stop current trading
            self.stop_terminal_trading(terminal_id)
            
            # Wait before restart
            time.sleep(self.restart_delay)
            
            # Reset error count
            with self.lock:
                if terminal_id in self.terminals:
                    self.terminals[terminal_id].consecutive_errors = 0
                    self.terminals[terminal_id].error_count = 0
            
            # Restart trading
            success = self.start_terminal_trading(terminal_id)
            
            if success:
                self.main_logger.info(f"[T{terminal_id}] Terminal restarted successfully")
            else:
                self.main_logger.error(f"[T{terminal_id}] Failed to restart terminal")
            
            return success
            
        except Exception as e:
            self.main_logger.error(f"[T{terminal_id}] Error during restart: {str(e)}")
            return False
    
    def update_terminal_activity(self, terminal_id: str, activity_type: str = "general") -> None:
        """Update last activity time for a terminal."""
        with self.lock:
            if terminal_id in self.terminals:
                self.terminals[terminal_id].last_activity = datetime.now()
                
                if activity_type == "trade":
                    self.terminals[terminal_id].last_trade = datetime.now()
                    self.terminals[terminal_id].trades_today += 1
                elif activity_type == "signal":
                    # Signal generation counts as activity
                    pass
    
    def get_terminal_status(self, terminal_id: str) -> Optional[TerminalStatus]:
        """Get status for a specific terminal."""
        with self.lock:
            return self.terminals.get(terminal_id)
    
    def get_all_statuses(self) -> Dict[str, TerminalStatus]:
        """Get status for all terminals."""
        with self.lock:
            return self.terminals.copy()
    
    def get_active_terminals(self) -> List[str]:
        """Get list of actively trading terminals."""
        with self.lock:
            return [
                terminal_id for terminal_id, status in self.terminals.items()
                if status.is_trading and self.check_terminal_health(terminal_id)
            ]
    
    def start_monitoring(self) -> None:
        """Start background monitoring of all terminals."""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.main_logger.info("Starting terminal monitoring...")
        
        def monitoring_loop():
            while self.monitoring_active:
                try:
                    self._monitor_terminals()
                    time.sleep(self.check_interval)
                except Exception as e:
                    self.main_logger.error(f"Error in terminal monitoring: {str(e)}")
                    time.sleep(self.check_interval)
        
        self.monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
        self.monitoring_thread.start()
    
    def stop_monitoring(self) -> None:
        """Stop background monitoring."""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        self.main_logger.info("Terminal monitoring stopped")
    
    def _monitor_terminals(self) -> None:
        """Internal method to monitor all terminals."""
        with self.lock:
            active_count = 0
            failed_terminals = []
            
            for terminal_id, status in self.terminals.items():
                if self.check_terminal_health(terminal_id):
                    active_count += 1
                    if not status.is_trading:
                        # Try to restart inactive but healthy terminal
                        self.main_logger.warning(f"[T{terminal_id}] Terminal healthy but not trading, restarting...")
                        self.restart_terminal(terminal_id)
                else:
                    failed_terminals.append(terminal_id)
                    status.consecutive_errors += 1
            
            # Log monitoring summary
            total_terminals = len(self.terminals)
            self.main_logger.info(
                f"MONITORING | Active: {active_count}/{total_terminals} | "
                f"Failed: {len(failed_terminals)}"
            )
            
            # Attempt to restart failed terminals (but be more conservative)
            for terminal_id in failed_terminals:
                terminal_status = self.terminals[terminal_id]
                if terminal_status.consecutive_errors >= self.max_consecutive_errors:
                    # Only restart if terminal has been failing for a while
                    if terminal_status.last_activity:
                        time_since_last_activity = datetime.now() - terminal_status.last_activity
                        if time_since_last_activity > timedelta(minutes=5):  # 5 minutes of inactivity
                            self.main_logger.warning(f"[T{terminal_id}] Max errors reached and inactive for 5+ minutes, attempting restart...")
                            self.restart_terminal(terminal_id)
                        else:
                            self.main_logger.info(f"[T{terminal_id}] Has errors but recently active, not restarting yet")
                    else:
                        self.main_logger.warning(f"[T{terminal_id}] No activity recorded, attempting restart...")
                        self.restart_terminal(terminal_id)
    
    def generate_status_report(self) -> Dict[str, any]:
        """Generate comprehensive status report."""
        with self.lock:
            active_terminals = self.get_active_terminals()
            total_trades_today = sum(status.trades_today for status in self.terminals.values())
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_terminals': len(self.terminals),
                    'active_terminals': len(active_terminals),
                    'inactive_terminals': len(self.terminals) - len(active_terminals),
                    'total_trades_today': total_trades_today
                },
                'terminals': {
                    terminal_id: {
                        'is_trading': status.is_trading,
                        'status_message': status.status_message,
                        'trades_today': status.trades_today,
                        'error_count': status.error_count,
                        'consecutive_errors': status.consecutive_errors,
                        'last_activity': status.last_activity.isoformat() if status.last_activity else None,
                        'last_trade': status.last_trade.isoformat() if status.last_trade else None
                    }
                    for terminal_id, status in self.terminals.items()
                }
            }
            
            return report
    
    def save_status_report(self) -> str:
        """Save status report to file."""
        report = self.generate_status_report()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = Path("logs/monitoring") / f"terminal_status_{timestamp}.json"
        
        report_file.parent.mkdir(exist_ok=True)
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        return str(report_file)

# Global instance
terminal_manager = TerminalManager()

def get_terminal_manager() -> TerminalManager:
    """Get the global terminal manager instance."""
    return terminal_manager
